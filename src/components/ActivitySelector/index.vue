<template>
  <el-select v-model="actId" placeholder="点击选择活动" @change="$emit('change', $event)" filterable :clearable="clearable" :disabled="disabled" :multiple="multiple">
    <el-option v-for="item in selectActList" :value="item.act_id" :label="item.act_name+'('+item.act_id+')'" :key="item.act_id"  />
  </el-select>
</template>

<script>
import ActConfigVersion from '@/api/activity/actConfigVersion'
export default {
  name: "ActivitySelector",
  data() {
    return {
      selectActList: [],
      actId: this.convertValue(this.value)
    }
  },
  props: {
    value: [Number, String, Array],
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  beforeMount() {
    this.loadActInfos()
  },
  methods:{
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res && res.result === 200) {
            this.selectActList = res.data
          }
        }
      )
    },
    convertValue(val) {
      // Handle empty string, null, undefined cases
      if (val === '' || val === null || val === undefined) {
        return ''
      }
      // Convert string numbers to actual numbers for single select
      if (typeof val === 'string' && val !== '' && !isNaN(val)) {
        return Number(val)
      }
      return val
    }
  },
  watch: {
    actId: function (val) {
      this.$emit('input', val)
      this.$emit('update:value', val)
    },
    value: function (val) {
      this.actId = this.convertValue(val)
    }
  }
}
</script>

<style scoped lang="scss">

</style>
