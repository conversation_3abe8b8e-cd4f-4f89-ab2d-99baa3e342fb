import request from '@/utils/request'

export default {
  loadComponentConfig(actId, componentId, moduleName) {
    return request({
      url: 'cmptConfig/list?actId=' + actId + "&componentId=" + componentId + "&moduleName=" + moduleName,
      method: 'get',
    })
  },
  listCmptByActIds(actIds) {
    return request({
      url: 'cmptConfig/listCmptByActIds?actIds=' + actIds,
      method: 'get',
    })
  },

  multiCopyingCmpt(data) {
    return request({
      url: 'cmptConfig/mutliCopyCmpt',
      data: data,
      method: 'post',
    })
  },

  copyActComponent(data) {
    return request({
      url: 'cmptConfig/copyActComponent',
      method: 'post',
      data: data,
    })
  },
  loadComponentDefines() {
    return request({
      url: 'cmptConfig/listComponentDefine',
      method: 'post',
    })
  },
  saveActComponent(data) {
    return request({
      url: 'cmptConfig/saveActComponent',
      method: 'post',
      data: data,
    })
  },
  updateActComponent(data) {
    return request({
      url: 'cmptConfig/updateActComponent',
      method: 'post',
      data: data,
    })
  },
  deleteActComponent(data) {
    return request({
      url: 'cmptConfig/deleteActComponent',
      method: 'post',
      data: data,
    })
  },
  synComponentAttrDefine(actId, componentId) {
    return request({
      url: 'cmptConfig/synComponentAttrDefine',
      method: 'get',
      params: { actId, componentId }
    })
  },
  initActComponent(json) {
    return request({
      url: 'cmptConfig/initActComponent',
      method: 'post',
      data: json
    })
  },
  /**
   * 根据活动ID查询组件流
   * @param {number} actId 活动ID
   * @returns {Promise} 组件流列表
   */
  listComponentFlowByActId(actId) {
    return request({
      url: 'componentFlow/listByActId',
      method: 'get',
      params: { actId }
    })
  },

  /**
   * 根据源组件查询组件流
   * @param {number} actId 活动ID
   * @param {number} srcCmptId 源组件ID
   * @param {number} srcCmptUseInx 源组件使用序号
   * @returns {Promise} 组件流列表
   */
  listComponentFlowBySrcComponent(actId, srcCmptId, srcCmptUseInx) {
    return request({
      url: 'componentFlow/listBySrcComponent',
      method: 'get',
      params: { actId, srcCmptId, srcCmptUseInx }
    })
  },

  /**
   * 根据目标组件查询组件流
   * @param {number} actId 活动ID
   * @param {number} destCmptId 目标组件ID
   * @param {number} destCmptUseInx 目标组件使用序号
   * @returns {Promise} 组件流列表
   */
  listComponentFlowByDestComponent(actId, destCmptId, destCmptUseInx) {
    return request({
      url: 'componentFlow/listByDestComponent',
      method: 'get',
      params: { actId, destCmptId, destCmptUseInx }
    })
  },

  /**
   * 获取指定的组件流
   * @param {number} actId 活动ID
   * @param {number} srcCmptId 源组件ID
   * @param {number} srcCmptUseInx 源组件使用序号
   * @param {number} destCmptId 目标组件ID
   * @param {number} destCmptUseInx 目标组件使用序号
   * @returns {Promise} 组件流对象
   */
  getComponentFlow(actId, srcCmptId, srcCmptUseInx, destCmptId, destCmptUseInx) {
    return request({
      url: 'componentFlow/get',
      method: 'get',
      params: { actId, srcCmptId, srcCmptUseInx, destCmptId, destCmptUseInx }
    })
  },

  /**
   * 保存或更新组件流
   * @param {Object} componentFlow 组件流对象
   * @returns {Promise} 操作结果
   */
  saveOrUpdateComponentFlow(componentFlow) {
    return request({
      url: 'componentFlow/saveOrUpdate',
      method: 'post',
      data: componentFlow,
    })
  },

  /**
   * 删除指定的组件流
   * @param {number} actId 活动ID
   * @param {number} srcCmptId 源组件ID
   * @param {number} srcCmptUseInx 源组件使用序号
   * @param {number} destCmptId 目标组件ID
   * @param {number} destCmptUseInx 目标组件使用序号
   * @returns {Promise} 操作结果
   */
  removeComponentFlow(actId, srcCmptId, srcCmptUseInx, destCmptId, destCmptUseInx) {
    return request({
      url: 'componentFlow/remove',
      method: 'delete',
      params: { actId, srcCmptId, srcCmptUseInx, destCmptId, destCmptUseInx }
    })
  },

  /**
   * 删除指定活动的所有组件流
   * @param {number} actId 活动ID
   * @returns {Promise} 删除的记录数
   */
  removeComponentFlowByActId(actId) {
    return request({
      url: 'componentFlow/removeByActId',
      method: 'delete',
      params: { actId }
    })
  },
}
